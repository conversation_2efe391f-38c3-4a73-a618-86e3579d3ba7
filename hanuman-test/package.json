{"name": "hanuman-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/lodash": "^4.17.17", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "axios": "^1.9.0", "framer-motion": "^12.12.2", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "moment": "^2.30.1", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "ws": "^8.18.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}